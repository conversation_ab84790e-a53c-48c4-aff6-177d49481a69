"""Add HedgeFundFlow table

Revision ID: 5274886e5bee
Revises: 
Create Date: 2025-06-20 14:50:24.736989

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5274886e5bee'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('hedge_fund_flows',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('nodes', sa.JSON(), nullable=False),
    sa.Column('edges', sa.JSON(), nullable=False),
    sa.Column('viewport', sa.JSON(), nullable=True),
    sa.Column('is_template', sa.Boolean(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_hedge_fund_flows_id'), 'hedge_fund_flows', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_hedge_fund_flows_id'), table_name='hedge_fund_flows')
    op.drop_table('hedge_fund_flows')
    # ### end Alembic commands ###
