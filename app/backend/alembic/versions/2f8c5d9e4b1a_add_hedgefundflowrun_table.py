"""Add HedgeFundFlowRun table

Revision ID: 2f8c5d9e4b1a
Revises: 1b1feba3d897
Create Date: 2025-01-01 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2f8c5d9e4b1a'
down_revision: Union[str, None] = '1b1feba3d897'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('hedge_fund_flow_runs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('flow_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False, server_default='IDLE'),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('request_data', sa.JSON(), nullable=True),
    sa.Column('results', sa.JSON(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('run_number', sa.Integer(), nullable=False, server_default='1'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_hedge_fund_flow_runs_id'), 'hedge_fund_flow_runs', ['id'], unique=False)
    op.create_index(op.f('ix_hedge_fund_flow_runs_flow_id'), 'hedge_fund_flow_runs', ['flow_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_hedge_fund_flow_runs_flow_id'), table_name='hedge_fund_flow_runs')
    op.drop_index(op.f('ix_hedge_fund_flow_runs_id'), table_name='hedge_fund_flow_runs')
    op.drop_table('hedge_fund_flow_runs')
    # ### end Alembic commands ### 