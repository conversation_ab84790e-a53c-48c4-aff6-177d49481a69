# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# Environment Variables
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.cursorrules
.cursorignore
.cursorindexingignore

# OS
.DS_Store
Thumbs.db

# graph
*.png

# Txt files
*.txt

# PDF files
*.pdf

# Frontend
node_modules

# Outputs
outputs/

# Database files (users will have their own local databases)
*.db
*.db-journal
*.db-wal
*.db-shm
*.sqlite
*.sqlite3

# Alembic (keep migration files, but ignore generated/cache files)
app/backend/alembic/versions/__pycache__/